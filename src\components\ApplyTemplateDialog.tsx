
"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { MetadataTemplate } from '@/types';
import { useToast } from "@/hooks/use-toast";
import type { CreateTemplateData } from '@/services/templateService'; // Import the specific type for creation

interface ApplyTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  templates: MetadataTemplate[];
  onCreateTemplate: (newTemplateData: CreateTemplateData) => Promise<MetadataTemplate | null>;
  onApplyTemplate: (template: MetadataTemplate) => void;
  numFilesSelected: number;
}

const ApplyTemplateDialog: React.FC<ApplyTemplateDialogProps> = ({
  isOpen,
  onClose,
  templates,
  onCreateTemplate,
  onApplyTemplate,
  numFilesSelected,
}) => {
  const [activeTab, setActiveTab] = useState("apply");
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");

  const [newTemplateName, setNewTemplateName] = useState("");
  const [newTemplateTitle, setNewTemplateTitle] = useState("");
  const [newTemplateAuthor, setNewTemplateAuthor] = useState("");
  const [newTemplateSubject, setNewTemplateSubject] = useState("");
  const [newTemplateKeywords, setNewTemplateKeywords] = useState("");
  const [newTemplateProducer, setNewTemplateProducer] = useState("");
  const [newTemplateCreatorTool, setNewTemplateCreatorTool] = useState("");


  const { toast } = useToast();

  useEffect(() => {
    if (isOpen) {
      setSelectedTemplateId(templates.length > 0 ? templates[0].id : "");
      setNewTemplateName("");
      setNewTemplateTitle("");
      setNewTemplateAuthor("");
      setNewTemplateSubject("");
      setNewTemplateKeywords("");
      setNewTemplateProducer("");
      setNewTemplateCreatorTool("");
      setActiveTab(templates.length > 0 ? "apply" : "create");
    }
  }, [isOpen, templates]);

  const handleApplyExisting = () => {
    if (!selectedTemplateId) {
      toast({ title: "No Template Selected", description: "Please select a template to apply.", variant: "destructive" });
      return;
    }
    const templateToApply = templates.find(t => t.id === selectedTemplateId);
    if (templateToApply) {
      onApplyTemplate(templateToApply);
      onClose();
    } else {
       toast({ title: "Template Not Found", description: "The selected template could not be found.", variant: "destructive" });
    }
  };

  const handleCreateAndApply = async () => {
    if (!newTemplateName.trim()) {
      toast({ title: "Template Name Required", description: "Please enter a name for the new template.", variant: "destructive" });
      return;
    }
    // Prepare data conforming to CreateTemplateData
    // Optional fields will be empty strings if not filled, which is acceptable for Firestore.
    const templateCoreData: CreateTemplateData = {
      name: newTemplateName.trim(),
      title: newTemplateTitle.trim(),
      author: newTemplateAuthor.trim(),
      subject: newTemplateSubject.trim(),
      keywords: newTemplateKeywords.trim(),
      producer: newTemplateProducer.trim(),
      creatorTool: newTemplateCreatorTool.trim(),
    };

    const createdTemplate = await onCreateTemplate(templateCoreData);

    if (createdTemplate) {
      onApplyTemplate(createdTemplate);
      onClose();
    }
  };

  if (numFilesSelected === 0 && isOpen) {
    onClose();
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-card text-card-foreground">
        <DialogHeader>
          <DialogTitle>Apply Metadata Template</DialogTitle>
          <DialogDescription>
            Apply a template to the selected {numFilesSelected} file(s).
            Fields defined in the template will overwrite existing values.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-2">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="apply" disabled={templates.length === 0}>Apply Existing</TabsTrigger>
            <TabsTrigger value="create">Create New & Apply</TabsTrigger>
          </TabsList>
          <TabsContent value="apply" className="py-4 space-y-4">
            {templates.length > 0 ? (
              <>
                <Label htmlFor="select-template">Select Template</Label>
                <Select value={selectedTemplateId} onValueChange={setSelectedTemplateId}>
                  <SelectTrigger id="select-template">
                    <SelectValue placeholder="Choose a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map(template => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button onClick={handleApplyExisting} className="w-full" disabled={!selectedTemplateId}>
                  Apply Selected Template
                </Button>
              </>
            ) : (
              <p className="text-sm text-muted-foreground text-center">No templates created yet. Go to "Create New & Apply" to start.</p>
            )}
          </TabsContent>
          <TabsContent value="create" className="py-4 space-y-3">
            <div className="space-y-1">
              <Label htmlFor="new-template-name">Template Name (Required)</Label>
              <Input id="new-template-name" value={newTemplateName} onChange={e => setNewTemplateName(e.target.value)} placeholder="e.g., Company Standard, Blog Post SEO" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-template-title">Title</Label>
              <Input id="new-template-title" value={newTemplateTitle} onChange={e => setNewTemplateTitle(e.target.value)} placeholder="Template for Title (Optional)" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-template-author">Author</Label>
              <Input id="new-template-author" value={newTemplateAuthor} onChange={e => setNewTemplateAuthor(e.target.value)} placeholder="Template for Author (Optional)" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-template-subject">Subject</Label>
              <Input id="new-template-subject" value={newTemplateSubject} onChange={e => setNewTemplateSubject(e.target.value)} placeholder="Template for Subject (Optional)" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-template-keywords">Keywords (comma-separated)</Label>
              <Input id="new-template-keywords" value={newTemplateKeywords} onChange={e => setNewTemplateKeywords(e.target.value)} placeholder="template, keyword1, keyword2 (Optional)" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-template-producer">Producer</Label>
              <Input id="new-template-producer" value={newTemplateProducer} onChange={e => setNewTemplateProducer(e.target.value)} placeholder="Template for Producer (Optional)" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-template-creatorTool">Creator Tool</Label>
              <Input id="new-template-creatorTool" value={newTemplateCreatorTool} onChange={e => setNewTemplateCreatorTool(e.target.value)} placeholder="Template for Creator Tool (Optional)" />
            </div>
            <Button onClick={handleCreateAndApply} className="w-full" disabled={!newTemplateName.trim()}>
              Create & Apply Template
            </Button>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-2">
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApplyTemplateDialog;
