'use client';

import { useState, useEffect, type FormEvent } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Edit3, SearchCheck, Zap, Settings2 } from 'lucide-react';

const FeatureListItem = ({ icon: Icon, text }: { icon: React.ElementType, text: string }) => (
  <li className="flex items-start text-sm">
    <Icon className="h-5 w-5 text-accent mr-2 mt-0.5 shrink-0" />
    <span>{text}</span>
  </li>
);

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login, signup, user, loading: authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect to home if already logged in and auth is not loading
    if (!authLoading && user) {
      router.replace('/');
    }
  }, [user, authLoading, router]);

  const handleLogin = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    try {
      await login(email, password);
      // Redirect is handled by the useEffect above
    } catch (error) {
      // Error toast is handled within the login function in AuthContext
      console.error("Login page caught error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSignup = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    try {
      await signup(email, password);
      // Redirect is handled by the useEffect above
    } catch (error) {
      // Error toast is handled within the signup function in AuthContext
      console.error("Signup page caught error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };


  if (authLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading authentication...</p>
      </div>
    );
  }

  if (user) {
     return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Redirecting...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-background to-secondary/10 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl w-full space-y-10">
        {/* Enhanced Header and Description */}
        <div className="text-center">
          {/* Wrap the image in a div for the shape and shadow */}
          <div className="inline-flex items-center justify-center bg-secondary rounded-full p-4 shadow-lg mb-4 overflow-hidden mx-auto">
            <Image
              src="/ms-icon-150x150.png"
              alt="MetaPDF Logo"
 width={80}
 height={80}
 className="relative h-full w-full object-cover rounded-full"

              priority
              data-ai-hint="app icon"
            />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-primary mb-4">
            Welcome to MetaPDF
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground mb-6 max-w-2xl mx-auto">
            Effortlessly edit, optimize, and manage your PDF metadata. Boost SEO, streamline workflows, and take control of your documents.
          </p>
          <Card className="max-w-md mx-auto bg-card/70 backdrop-blur-sm mb-8 shadow-lg">
            <CardContent className="pt-6 text-left space-y-3">
                <FeatureListItem icon={Edit3} text="Comprehensive metadata editing for title, author, subject, and more."/>
                <FeatureListItem icon={SearchCheck} text="AI-powered keyword & subject suggestions to enhance SEO."/>
                <FeatureListItem icon={Zap} text="Bulk operations to edit or rename multiple files at once."/>
                <FeatureListItem icon={Settings2} text="Create and apply metadata templates for consistency (Pro feature)."/>
            </CardContent>
          </Card>
        </div>

        {/* Login/Signup Tabs */}
        <div className="max-w-md w-full mx-auto">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="signup">Sign Up</TabsTrigger>
            </TabsList>
            <TabsContent value="login">
              <Card className="shadow-md">
                <form onSubmit={handleLogin}>
                  <CardHeader>
                    <CardTitle>Login to Your Account</CardTitle>
                    <CardDescription>Enter your credentials to access your dashboard.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-1">
                      <Label htmlFor="login-email">Email address</Label>
                      <Input
                        id="login-email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        disabled={isSubmitting}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="login-password">Password</Label>
                      <Input
                        id="login-password"
                        name="password"
                        type="password"
                        autoComplete="current-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Password"
                        disabled={isSubmitting}
                      />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full" disabled={isSubmitting}>
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Login
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
            <TabsContent value="signup">
              <Card className="shadow-md">
                <form onSubmit={handleSignup}>
                  <CardHeader>
                    <CardTitle>Create Your MetaPDF Account</CardTitle>
                    <CardDescription>Join free to start managing your PDF metadata effectively.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-1">
                      <Label htmlFor="signup-email">Email address</Label>
                      <Input
                        id="signup-email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        disabled={isSubmitting}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="signup-password">Password</Label>
                      <Input
                        id="signup-password"
                        name="password"
                        type="password"
                        autoComplete="new-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Password (min. 6 characters)"
                        disabled={isSubmitting}
                      />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button type="submit" className="w-full" disabled={isSubmitting}>
                       {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Sign Up
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
