
// src/app/api/lemonsqueezy-webhook/route.ts
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import crypto from 'crypto';
import { firestore } from '@/lib/firebase';
import { doc, updateDoc, increment, serverTimestamp, setDoc, getDoc } from 'firebase/firestore';
import { PLANS, ADDON_PACKS, DEFAULT_PLAN_ID } from '@/config/plans';
import type { UserSubscriptionData } from '@/types';

const LEMON_SQUEEZY_SIGNING_SECRET = process.env.LEMON_SQUEEZY_SIGNING_SECRET;

if (!LEMON_SQUEEZY_SIGNING_SECRET) {
  console.warn(
    '[lemonsqueezy-webhook] WARNING: LEMON_SQUEEZY_SIGNING_SECRET is not set in environment variables. Webhook verification will fail.'
  );
}

async function verifySignature(request: NextRequest, rawBody: string): Promise<boolean> {
  if (!LEMON_SQUEEZY_SIGNING_SECRET) {
    console.error('[lemonsqueezy-webhook] Error: Lemon Squeezy signing secret is not configured. Cannot verify signature.');
    return false;
  }
  const signatureHeader = request.headers.get('X-Signature');
  if (!signatureHeader) {
    console.warn('[lemonsqueezy-webhook] Warning: No X-Signature header found on webhook request. Verification failed.');
    return false;
  }

  try {
    const hmac = crypto.createHmac('sha256', LEMON_SQUEEZY_SIGNING_SECRET);
    const digest = Buffer.from(hmac.update(rawBody).digest('hex'), 'utf8');
    const receivedSignature = Buffer.from(signatureHeader, 'utf8');
    
    if (digest.length !== receivedSignature.length) {
        console.warn('[lemonsqueezy-webhook] Warning: Signature length mismatch. Verification failed.');
        return false;
    }

    const isValid = crypto.timingSafeEqual(digest, receivedSignature);
    if (isValid) {
        console.log('[lemonsqueezy-webhook] Info: Webhook signature verified successfully.');
    } else {
        console.warn('[lemonsqueezy-webhook] Warning: Webhook signature verification failed (signatures do not match).');
    }
    return isValid;
  } catch (error) {
    console.error('[lemonsqueezy-webhook] Error: Exception during signature verification:', error);
    return false;
  }
}

function getTodayDateString(): string {
  return new Date().toISOString().split('T')[0]; // YYYY-MM-DD
}

export async function POST(request: NextRequest) {
  console.log('[lemonsqueezy-webhook] Info: Received POST request.');
  let rawBody;
  try {
    rawBody = await request.text();
  } catch (err) {
    console.error('[lemonsqueezy-webhook] Error: Failed reading request body:', err);
    return NextResponse.json({ error: 'Error reading request body' }, { status: 400 });
  }

  if (!await verifySignature(request, rawBody)) {
    console.warn('[lemonsqueezy-webhook] Error: Invalid Lemon Squeezy webhook signature. Request rejected.');
    return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
  }

  let event;
  try {
    event = JSON.parse(rawBody);
    console.log('[lemonsqueezy-webhook] Info: Successfully parsed JSON payload.');
  } catch (err) {
    console.error('[lemonsqueezy-webhook] Error: Failed parsing JSON payload:', err, 'Raw body was:', rawBody);
    return NextResponse.json({ error: 'Invalid JSON payload' }, { status: 400 });
  }

  const eventName = event.meta?.event_name;
  const customData = event.meta?.custom_data;
  const userId = customData?.user_id; 

  console.log(`[lemonsqueezy-webhook] Info: Processing event_name: "${eventName}", user_id from custom_data: "${userId || 'N/A'}"`);

  if (!userId) {
    console.warn(`[lemonsqueezy-webhook] Warning: Webhook received for event "${eventName}" but without user_id in meta.custom_data. Payload meta:`, JSON.stringify(event.meta));
    return NextResponse.json({ message: 'Webhook received but missing user_id in custom_data. Cannot process user-specific action.' }, { status: 400 });
  }

  const userRef = doc(firestore, 'users', userId);
  const today = getTodayDateString();

  try {
    console.log(`[lemonsqueezy-webhook] Info: Processing confirmed event: "${eventName}" for user: "${userId}"`);
    const attributes = event.data?.attributes;
    if (!attributes) {
        console.warn(`[lemonsqueezy-webhook] Warning: Event "${eventName}" for user "${userId}" has no 'data.attributes'. Cannot process further.`);
        return NextResponse.json({ error: `Event data attributes missing for ${eventName}` }, { status: 400 });
    }
    
    let variantId = attributes?.variant_id; 

    if (eventName === 'order_created' && attributes?.first_order_item?.variant_id) {
        variantId = attributes.first_order_item.variant_id;
        console.log(`[lemonsqueezy-webhook] Info: For 'order_created', using variant_id from first_order_item: "${variantId}" for user "${userId}".`);
    }
    
    const variantIdStr = variantId ? String(variantId) : null;
    console.log(`[lemonsqueezy-webhook] Info: Determined variant_id_str: "${variantIdStr || 'N/A'}" for event "${eventName}", user "${userId}".`);

    const plan = variantIdStr ? PLANS.find(p => p.lemonSqueezyVariantId === variantIdStr) : null;
    const addon = variantIdStr ? ADDON_PACKS.find(a => a.lemonSqueezyVariantId === variantIdStr) : null;

    if (plan) console.log(`[lemonsqueezy-webhook] Info: Matched plan: "${plan.name}" (ID: ${plan.id}) for user "${userId}".`);
    if (addon) console.log(`[lemonsqueezy-webhook] Info: Matched addon: "${addon.name}" (ID: ${addon.id}, Type: ${addon.type}) for user "${userId}".`);


    let updateData: Partial<UserSubscriptionData> & { [key: string]: any } = {};
    let fullSubscriptionUpdate: any = {}; 

    switch (eventName) {
      case 'order_created':
      case 'subscription_payment_succeeded':
      case 'subscription_created':
      case 'subscription_resumed':
      case 'subscription_unpaused':
      case 'subscription_updated':
        if (plan) {
          fullSubscriptionUpdate = {
            'subscription.planId': plan.id,
            'subscription.status': 'active',
            'subscription.currentPeriodEnd': attributes.renews_at ? new Date(attributes.renews_at) : null,
            'subscription.updatedAt': serverTimestamp(),
            'subscription.dailyUsage.keywordSuggestions': { count: 0, lastResetDate: today },
            'subscription.dailyUsage.subjectSuggestions': { count: 0, lastResetDate: today },
            'subscription.cancelAtPeriodEnd': !!attributes.ends_at,
          };
          await updateDoc(userRef, fullSubscriptionUpdate);
          console.log(`[lemonsqueezy-webhook] Success: User "${userId}" updated to plan "${plan.name}" (status: active) via event "${eventName}". Renews at: ${attributes.renews_at || 'N/A'}.`);
        } else if (addon && (eventName === 'order_created' || eventName === 'subscription_payment_succeeded')) { 
          const addonUpdatePath = `subscription.addons.${addon.type}`;
          updateData[addonUpdatePath] = increment(addon.quantity);
          updateData['subscription.updatedAt'] = serverTimestamp();
          await updateDoc(userRef, updateData);
          console.log(`[lemonsqueezy-webhook] Success: User "${userId}" received ${addon.quantity} of "${addon.type}" addons via event "${eventName}".`);
        } else if (!plan && !addon && variantIdStr) {
           console.warn(`[lemonsqueezy-webhook] Warning: Unknown variant_id "${variantIdStr}" for event "${eventName}", user "${userId}". No plan or addon match.`);
        } else if (!variantIdStr && (eventName === 'subscription_payment_succeeded' || eventName === 'subscription_resumed' || eventName === 'subscription_unpaused')) {
            const userDocSnap = await getDoc(userRef);
            if(userDocSnap.exists() && userDocSnap.data()?.subscription?.planId){
                 const existingPlanId = userDocSnap.data()!.subscription.planId;
                 console.log(`[lemonsqueezy-webhook] Info: Event "${eventName}" for user "${userId}" for existing plan "${existingPlanId}". Updating renewal/status.`);
                 await updateDoc(userRef, {
                    'subscription.status': 'active',
                    'subscription.currentPeriodEnd': attributes.renews_at ? new Date(attributes.renews_at) : null,
                    'subscription.updatedAt': serverTimestamp(),
                    'subscription.cancelAtPeriodEnd': !!attributes.ends_at,
                 });
                 console.log(`[lemonsqueezy-webhook] Success: User "${userId}" subscription status/renewal updated for plan "${existingPlanId}" via event "${eventName}".`);
            } else {
                 console.warn(`[lemonsqueezy-webhook] Warning: Event "${eventName}" for user "${userId}" but could not determine existing plan to update renewal. User doc exists: ${userDocSnap.exists()}`);
            }
        } else {
            console.log(`[lemonsqueezy-webhook] Info: Event "${eventName}" for user "${userId}" did not result in a specific plan/addon update action for variant "${variantIdStr || 'N/A'}".`);
        }
        break;

      case 'subscription_cancelled':
      case 'subscription_expired':
      case 'subscription_paused':
        fullSubscriptionUpdate = {
            'subscription.planId': DEFAULT_PLAN_ID,
            'subscription.status': eventName === 'subscription_paused' ? 'paused' : 'inactive',
            'subscription.currentPeriodEnd': attributes.ends_at ? new Date(attributes.ends_at) : null,
            'subscription.updatedAt': serverTimestamp(),
            'subscription.dailyUsage.keywordSuggestions': { count: 0, lastResetDate: today },
            'subscription.dailyUsage.subjectSuggestions': { count: 0, lastResetDate: today },
        };
        if (eventName === 'subscription_cancelled' && attributes.ends_at && new Date(attributes.ends_at) > new Date()) {
            const userDocSnap = await getDoc(userRef);
            const currentPlanId = userDocSnap.exists() ? userDocSnap.data()?.subscription?.planId : DEFAULT_PLAN_ID;
            fullSubscriptionUpdate['subscription.planId'] = currentPlanId; 
            fullSubscriptionUpdate['subscription.status'] = 'active'; 
            fullSubscriptionUpdate['subscription.cancelAtPeriodEnd'] = true;
            console.log(`[lemonsqueezy-webhook] Info: Subscription for user "${userId}" (plan: ${currentPlanId}) marked to cancel at period end: ${attributes.ends_at}.`);
        } else {
            console.log(`[lemonsqueezy-webhook] Info: Subscription for user "${userId}" set to plan "${DEFAULT_PLAN_ID}" (status: ${fullSubscriptionUpdate['subscription.status']}) due to event "${eventName}". Ends at: ${attributes.ends_at || 'N/A'}`);
        }
        await updateDoc(userRef, fullSubscriptionUpdate);
        console.log(`[lemonsqueezy-webhook] Success: User "${userId}" subscription updated due to event "${eventName}".`);
        break;
      
      case 'order_refunded':
        console.log(`[lemonsqueezy-webhook] Info: Processing 'order_refunded' for user "${userId}", variant "${variantIdStr || 'N/A'}".`);
        if (plan) {
            await updateDoc(userRef, {
                'subscription.planId': DEFAULT_PLAN_ID,
                'subscription.status': 'inactive',
                'subscription.updatedAt': serverTimestamp(),
                'subscription.dailyUsage.keywordSuggestions': { count: 0, lastResetDate: today },
                'subscription.dailyUsage.subjectSuggestions': { count: 0, lastResetDate: today },
            });
            console.log(`[lemonsqueezy-webhook] Success: Plan for user "${userId}" reverted to free due to refund on variant "${variantIdStr}".`);
        } else if (addon) {
            console.log(`[lemonsqueezy-webhook] Info: Addon purchase for variant "${variantIdStr}" refunded for user "${userId}". Addon counts are not automatically decremented by this webhook logic for refunds.`);
        } else {
            console.log(`[lemonsqueezy-webhook] Info: Order refunded for user "${userId}", variant "${variantIdStr || 'N/A'}". No specific plan or addon action taken for unknown variant.`);
        }
        break;

      default:
        console.log(`[lemonsqueezy-webhook] Info: Received unhandled Lemon Squeezy event: "${eventName}" for user "${userId}". No action taken.`);
        return NextResponse.json({ message: `Webhook received (unhandled event: ${eventName})` });
    }

    console.log(`[lemonsqueezy-webhook] Info: Successfully processed event "${eventName}" for user "${userId}".`);
    return NextResponse.json({ message: 'Webhook processed successfully' });
  } catch (error) {
    console.error(`[lemonsqueezy-webhook] CRITICAL ERROR: Error processing webhook event "${eventName}" for user "${userId}":`, error);
    return NextResponse.json({ error: 'Internal server error while processing webhook' }, { status: 500 });
  }
}
