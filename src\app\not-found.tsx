'use client';

import { FileQuestion, Home, Search, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

// Force dynamic rendering to avoid SSR issues with event handlers
export const dynamic = 'force-dynamic';

/**
 * Global 404 Not Found Page
 * 
 * This page is shown when a user navigates to a route that doesn't exist.
 * It provides helpful navigation options and suggestions.
 */
export default function NotFoundPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-16 w-16 text-muted-foreground">
            <FileQuestion className="h-full w-full" />
          </div>
          <CardTitle className="text-3xl">Page Not Found</CardTitle>
          <CardDescription className="text-lg">
            The page you're looking for doesn't exist or has been moved.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">What you can do:</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Check the URL for any typos</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Go back to the previous page</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Visit our homepage to start fresh</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Use the navigation menu to find what you need</span>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Popular Pages:</h3>
            <div className="grid gap-2 text-sm">
              <Link 
                href="/" 
                className="flex items-center gap-2 p-2 rounded hover:bg-muted transition-colors"
              >
                <Home className="h-4 w-4" />
                <span>PDF Editor - Upload and edit your PDFs</span>
              </Link>
              <Link 
                href="/pricing" 
                className="flex items-center gap-2 p-2 rounded hover:bg-muted transition-colors"
              >
                <Search className="h-4 w-4" />
                <span>Pricing - View our plans and features</span>
              </Link>
              <Link 
                href="/profile" 
                className="flex items-center gap-2 p-2 rounded hover:bg-muted transition-colors"
              >
                <Search className="h-4 w-4" />
                <span>Profile - Manage your account settings</span>
              </Link>
            </div>
          </div>

          <div className="flex flex-col gap-3 sm:flex-row">
            <Button asChild className="flex-1">
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Go to Homepage
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={() => typeof window !== 'undefined' && window.history.back()}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            <p>
              Still can't find what you're looking for?{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary hover:underline"
              >
                Contact Support
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
