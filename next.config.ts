
import type {NextConfig} from 'next';
import withPWAInit from 'next-pwa'; // Renamed import to avoid conflict

const nextConfig: NextConfig = {
  /* config options here */
  productionBrowserSourceMaps: true, // Explicitly enable source maps
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

const withPWA = withPWAInit({
  dest: 'public',
  register: true,
  skipWaiting: true,
  // You can disable PWA in development to avoid issues with HMR
  // disable: process.env.NODE_ENV === 'development', 
});

export default withPWA(nextConfig);
