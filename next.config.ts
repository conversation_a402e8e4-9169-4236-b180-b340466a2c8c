
import type {NextConfig} from 'next';
// @ts-ignore - next-pwa doesn't have proper TypeScript definitions
import withPWAInit from 'next-pwa'; // Renamed import to avoid conflict

const nextConfig: NextConfig = {
  /* config options here */
  productionBrowserSourceMaps: true, // Explicitly enable source maps
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Additional security and performance configurations
  poweredByHeader: false, // Remove X-Powered-By header
  compress: true, // Enable gzip compression

  // Security headers (additional to middleware)
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
        ],
      },
      // Specific headers for static assets
      {
        source: '/(.*)\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

const withPWA = withPWAInit({
  dest: 'public',
  register: true,
  skipWaiting: true,
  // You can disable PWA in development to avoid issues with HMR
  // disable: process.env.NODE_ENV === 'development', 
});

export default withPWA(nextConfig);
