{"version": 3, "file": "sw.js", "sources": ["../../AppData/Local/Temp/8aff6a7e112a23911acd8d3d7c142d50/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-routing/registerRoute.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {CacheFirst as workbox_strategies_CacheFirst} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-strategies/CacheFirst.mjs';\nimport {StaleWhileRevalidate as workbox_strategies_StaleWhileRevalidate} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-strategies/StaleWhileRevalidate.mjs';\nimport {RangeRequestsPlugin as workbox_range_requests_RangeRequestsPlugin} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-range-requests/RangeRequestsPlugin.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'C:/Users/<USER>/Documents/studio/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\nimportScripts(\n  \n);\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"/_next/app-build-manifest.json\",\n    \"revision\": \"0df0882902f29633a01d6e0383ebb60c\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/109-a26e06f44825baa0.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/109-a26e06f44825baa0.js.map\",\n    \"revision\": \"88e60283b11f988c32371ff217ed5470\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/120.6b09b1e38437fad8.js\",\n    \"revision\": \"6b09b1e38437fad8\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/120.6b09b1e38437fad8.js.map\",\n    \"revision\": \"d1d68b553881058fa91e8dea16da3871\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/145.4ba2c65d3d40adea.js\",\n    \"revision\": \"4ba2c65d3d40adea\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/145.4ba2c65d3d40adea.js.map\",\n    \"revision\": \"1a17deb49a5b7428206a256a857109a3\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/22.8f361c77e7e0cddb.js\",\n    \"revision\": \"8f361c77e7e0cddb\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/22.8f361c77e7e0cddb.js.map\",\n    \"revision\": \"15a82a6aacf5b22c5d5148b5c27459ed\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/278.ef23024d06e05c7c.js\",\n    \"revision\": \"ef23024d06e05c7c\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/278.ef23024d06e05c7c.js.map\",\n    \"revision\": \"da64c254fe687341cea760362c76f36b\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/283-f9bcfac63d267fb7.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/283-f9bcfac63d267fb7.js.map\",\n    \"revision\": \"b089a880534439b71a085bab5a40447d\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/341.ce3ca6285ffdc4bb.js\",\n    \"revision\": \"ce3ca6285ffdc4bb\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/341.ce3ca6285ffdc4bb.js.map\",\n    \"revision\": \"133c19f487a97b281184636b589ab2a6\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/352-518ce39b001c5904.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/352-518ce39b001c5904.js.map\",\n    \"revision\": \"c1e42531f31a1e0ebd39677cfc8acd50\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/36b4fd63-03b371622753064e.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/36b4fd63-03b371622753064e.js.map\",\n    \"revision\": \"25616df09f013133e7ec5706eca6a60d\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/434.1b91a3120d97328f.js\",\n    \"revision\": \"1b91a3120d97328f\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/434.1b91a3120d97328f.js.map\",\n    \"revision\": \"3c03f6592681877691bfa27708eac5c7\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/439-dbc43f424bbce82c.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/439-dbc43f424bbce82c.js.map\",\n    \"revision\": \"48dbc14b3b1d59e79c6c8b1d9e6f31c9\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/472.8a187b90a4661754.js\",\n    \"revision\": \"8a187b90a4661754\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/472.8a187b90a4661754.js.map\",\n    \"revision\": \"4a05e44a5463aa5905b4dada1ae678a2\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/490-fec414c282547b28.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/490-fec414c282547b28.js.map\",\n    \"revision\": \"61f46406bd2f7be9764704e65253f62d\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/4bd1b696-616e6199161d59c2.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/4bd1b696-616e6199161d59c2.js.map\",\n    \"revision\": \"ed120626c38e2d324f0cf238dc7e0124\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/568.85e2df62f940e577.js\",\n    \"revision\": \"85e2df62f940e577\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/568.85e2df62f940e577.js.map\",\n    \"revision\": \"11620bfdfece2d051100612e4f8be517\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/649-a9f129aec8866aba.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/649-a9f129aec8866aba.js.map\",\n    \"revision\": \"02527518caafc72adad2f2ac37d2eba1\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/66.2827bb70436b0863.js\",\n    \"revision\": \"2827bb70436b0863\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/66.2827bb70436b0863.js.map\",\n    \"revision\": \"caf2bd0670926f616cadd6014ec3026a\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/684-e3f7188fea84d50f.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/684-e3f7188fea84d50f.js.map\",\n    \"revision\": \"aa35dd4f6f6e3f88adab5dee9b3903e2\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/686.cf1810e717426ad5.js\",\n    \"revision\": \"cf1810e717426ad5\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/686.cf1810e717426ad5.js.map\",\n    \"revision\": \"9c4d83ad76d89d160d313642d954343b\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/726-fac6688b2b713a55.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/726-fac6688b2b713a55.js.map\",\n    \"revision\": \"ebf6b5976d1e878e114b55f201cde7d6\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/729-13b62f43fe7e72c5.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/729-13b62f43fe7e72c5.js.map\",\n    \"revision\": \"8177106621fd1e5ce03d3735ec737ab9\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/7a49ec60.b09dec019699eca2.js\",\n    \"revision\": \"b09dec019699eca2\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/7a49ec60.b09dec019699eca2.js.map\",\n    \"revision\": \"5fdb32ce53471454f940f315139c32c3\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/827.254c9fc04234f9d1.js\",\n    \"revision\": \"254c9fc04234f9d1\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/827.254c9fc04234f9d1.js.map\",\n    \"revision\": \"01ddd74b64c9aa34e7188501aa56ac5b\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/846-0cc2f86b9450866c.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/846-0cc2f86b9450866c.js.map\",\n    \"revision\": \"9e11e9aed66357e2699863195bf7c61b\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/874-cf2b8667131974a8.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/874-cf2b8667131974a8.js.map\",\n    \"revision\": \"16bdbde3253d7902121b09188dad437d\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/877-c6be7818bc954c02.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/877-c6be7818bc954c02.js.map\",\n    \"revision\": \"8847052a195f7dc9ab75dc89f6889e80\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/923-83d1b6fc569cd243.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/923-83d1b6fc569cd243.js.map\",\n    \"revision\": \"4611fe6ad6e165744892d457823e18b4\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/925.83e785f52a48491c.js\",\n    \"revision\": \"83e785f52a48491c\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/925.83e785f52a48491c.js.map\",\n    \"revision\": \"87ac1a5a867f40f33ff8057878843a4c\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/936.420928a3ae890f2a.js\",\n    \"revision\": \"420928a3ae890f2a\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/936.420928a3ae890f2a.js.map\",\n    \"revision\": \"eb0a90147d044785612bc44fb2cc650e\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/947.0f570e933a373a42.js\",\n    \"revision\": \"0f570e933a373a42\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/947.0f570e933a373a42.js.map\",\n    \"revision\": \"e034057e3f9215e32032854d814f98e9\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/_not-found/page-52197054bfa2d47e.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/api/lemonsqueezy-webhook/route-4f9516ddeaf98175.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/error-bc4c724183b6f758.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/error-bc4c724183b6f758.js.map\",\n    \"revision\": \"cb183461d54eaa9c57a36463e234e848\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/layout-3076f4fbe6220ea9.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/layout-3076f4fbe6220ea9.js.map\",\n    \"revision\": \"e621031d45347737f124da5051832fc8\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/login/page-27e8eeea2187f439.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/login/page-27e8eeea2187f439.js.map\",\n    \"revision\": \"b8063c9bac969c22134676614a0e9d49\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/not-found-95e6a61d8dcf5954.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/not-found-95e6a61d8dcf5954.js.map\",\n    \"revision\": \"417e2bb6d6ef7cc0aa4082e9500c0cd6\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/page-630e179a217915ab.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/page-630e179a217915ab.js.map\",\n    \"revision\": \"6bd537d00ed7089d70b884de4ccb4918\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/pricing/page-e6bb9493d95836d8.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/pricing/page-e6bb9493d95836d8.js.map\",\n    \"revision\": \"fcd5f337f7a3ac12b98c406b3769bf85\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/privacy-policy/page-e83f8250f73d5bf7.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/profile/page-f03e78be993e4b4a.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/profile/page-f03e78be993e4b4a.js.map\",\n    \"revision\": \"620ce13319668ce443640dcc82d0f408\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/sitemap.xml/route-39e2aae6f01a1b1a.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/app/terms-of-service/page-47c1748896fed30b.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/bc9e92e6-4af5c7e2d442ae23.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/bc9e92e6-4af5c7e2d442ae23.js.map\",\n    \"revision\": \"a1f04d0c91a505d8fe7d1b677cd5fc7b\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/framework-36a6e1d299564510.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/framework-36a6e1d299564510.js.map\",\n    \"revision\": \"bfcc657efda0c48f1c944d82707dc881\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/main-75ee158c47caf6ad.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/main-75ee158c47caf6ad.js.map\",\n    \"revision\": \"c5245ef6f47efa51a66c9a3997ea4f9c\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/main-app-96d22779a9dc5be7.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/main-app-96d22779a9dc5be7.js.map\",\n    \"revision\": \"683e6a18efc0c08b00896bc50159fab9\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/pages/_app-e852dd688ddb9819.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/pages/_app-e852dd688ddb9819.js.map\",\n    \"revision\": \"1febccdefd5a9790e4fb4cc4ead9e97b\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/pages/_error-aa04d697157cd98d.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/pages/_error-aa04d697157cd98d.js.map\",\n    \"revision\": \"6135b6271506d520da63692a81322be0\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/polyfills-42372ed130431b0a.js\",\n    \"revision\": \"846118c33b2c0e922d7b3a7676f81f6f\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/webpack-9ee681ec88204992.js\",\n    \"revision\": \"ryka24KYxn28r94OcbxzR\"\n  },\n  {\n    \"url\": \"/_next/static/chunks/webpack-9ee681ec88204992.js.map\",\n    \"revision\": \"d91c5b70619ead1e9f3c62ebdbf26d06\"\n  },\n  {\n    \"url\": \"/_next/static/css/17b7606306b891f3.css\",\n    \"revision\": \"17b7606306b891f3\"\n  },\n  {\n    \"url\": \"/_next/static/css/17b7606306b891f3.css.map\",\n    \"revision\": \"03bb1c068f9d98dbce5361cbd014818d\"\n  },\n  {\n    \"url\": \"/_next/static/media/26a46d62cd723877-s.woff2\",\n    \"revision\": \"befd9c0fdfa3d8a645d5f95717ed6420\"\n  },\n  {\n    \"url\": \"/_next/static/media/55c55f0601d81cf3-s.woff2\",\n    \"revision\": \"43828e14271c77b87e3ed582dbff9f74\"\n  },\n  {\n    \"url\": \"/_next/static/media/581909926a08bbc8-s.woff2\",\n    \"revision\": \"f0b86e7c24f455280b8df606b89af891\"\n  },\n  {\n    \"url\": \"/_next/static/media/7bdd799813739e3e-s.woff2\",\n    \"revision\": \"25a3d8f89bcf28ece878905cc3657907\"\n  },\n  {\n    \"url\": \"/_next/static/media/8e9860b6e62d6359-s.woff2\",\n    \"revision\": \"01ba6c2a184b8cba08b0d57167664d75\"\n  },\n  {\n    \"url\": \"/_next/static/media/97e0cb1ae144a2a9-s.woff2\",\n    \"revision\": \"e360c61c5bd8d90639fd4503c829c2dc\"\n  },\n  {\n    \"url\": \"/_next/static/media/c7f848bed9dd25a3-s.p.woff2\",\n    \"revision\": \"86a20ec925c014f6e10d3e504e1d96fe\"\n  },\n  {\n    \"url\": \"/_next/static/media/df0a9ae256c0569c-s.woff2\",\n    \"revision\": \"d54db44de5ccb18886ece2fda72bdfe0\"\n  },\n  {\n    \"url\": \"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\n    \"revision\": \"65850a373e258f1c897a2b3d75eb74de\"\n  },\n  {\n    \"url\": \"/_next/static/ryka24KYxn28r94OcbxzR/_buildManifest.js\",\n    \"revision\": \"1e517d59c6fa9b2a1d1ef841f2c37966\"\n  },\n  {\n    \"url\": \"/_next/static/ryka24KYxn28r94OcbxzR/_ssgManifest.js\",\n    \"revision\": \"b6652df95db52feb4daf4eca35380933\"\n  },\n  {\n    \"url\": \"/android-icon-144x144.png\",\n    \"revision\": \"f1eecb41f260e61c0719825628e1eac2\"\n  },\n  {\n    \"url\": \"/android-icon-192x192.png\",\n    \"revision\": \"8befd7f555fa7b3fb29cc9266582f609\"\n  },\n  {\n    \"url\": \"/android-icon-36x36.png\",\n    \"revision\": \"8ba3a0271ca2a65a2e05868860abaddc\"\n  },\n  {\n    \"url\": \"/android-icon-48x48.png\",\n    \"revision\": \"e5d226064d30dbba5ede6581c4b95a9d\"\n  },\n  {\n    \"url\": \"/android-icon-72x72.png\",\n    \"revision\": \"b7a6e5b936cf5b24ef07c9b1e895c985\"\n  },\n  {\n    \"url\": \"/android-icon-96x96.png\",\n    \"revision\": \"75ae21815a09e2c071be92a20f6de1af\"\n  },\n  {\n    \"url\": \"/apple-icon-114x114.png\",\n    \"revision\": \"6fc3af87eda1960a908e2903d0f387c3\"\n  },\n  {\n    \"url\": \"/apple-icon-120x120.png\",\n    \"revision\": \"17c2b9a1470033f9d9a30f01d11a2bde\"\n  },\n  {\n    \"url\": \"/apple-icon-144x144.png\",\n    \"revision\": \"f1eecb41f260e61c0719825628e1eac2\"\n  },\n  {\n    \"url\": \"/apple-icon-152x152.png\",\n    \"revision\": \"3282becf646f2e1379f602ccb94b4614\"\n  },\n  {\n    \"url\": \"/apple-icon-180x180.png\",\n    \"revision\": \"7543fbda7bd70524138ca55fb29aa11b\"\n  },\n  {\n    \"url\": \"/apple-icon-57x57.png\",\n    \"revision\": \"97a179f47332cfca66e1b3aa92689f19\"\n  },\n  {\n    \"url\": \"/apple-icon-60x60.png\",\n    \"revision\": \"18dd37bc662ac635f12e918eccf8c5fa\"\n  },\n  {\n    \"url\": \"/apple-icon-72x72.png\",\n    \"revision\": \"b7a6e5b936cf5b24ef07c9b1e895c985\"\n  },\n  {\n    \"url\": \"/apple-icon-76x76.png\",\n    \"revision\": \"ec3385e164f7c90d46c7795e0d6eb26b\"\n  },\n  {\n    \"url\": \"/apple-icon-precomposed.png\",\n    \"revision\": \"2e295930b910011f20a9e79946e01706\"\n  },\n  {\n    \"url\": \"/apple-icon.png\",\n    \"revision\": \"2e295930b910011f20a9e79946e01706\"\n  },\n  {\n    \"url\": \"/browserconfig.xml\",\n    \"revision\": \"dd4e16583a7808322c706b339cd9860d\"\n  },\n  {\n    \"url\": \"/favicon-16x16.png\",\n    \"revision\": \"13925703f7735841f9a693d0adc15f40\"\n  },\n  {\n    \"url\": \"/favicon-96x96.png\",\n    \"revision\": \"75ae21815a09e2c071be92a20f6de1af\"\n  },\n  {\n    \"url\": \"/manifest.json\",\n    \"revision\": \"f2bd2a3d90fc22f62c6aeb5735e30385\"\n  },\n  {\n    \"url\": \"/ms-icon-150x150.png\",\n    \"revision\": \"0d207704917b0f716f7bbe26f51ec4a2\"\n  },\n  {\n    \"url\": \"/ms-icon-310x310.png\",\n    \"revision\": \"4c45ca4d812d46aa42a7e74abee58eff\"\n  },\n  {\n    \"url\": \"/ms-icon-70x70.png\",\n    \"revision\": \"577bdc37a3304c5eb227e8ad113f3b7e\"\n  },\n  {\n    \"url\": \"/robots.txt\",\n    \"revision\": \"787a02d8f245aa1641dda1ab9eb9856d\"\n  }\n], {\n  \"ignoreURLParametersMatching\": []\n});\nworkbox_precaching_cleanupOutdatedCaches();\n\n\n\nworkbox_routing_registerRoute(\"/\", new workbox_strategies_NetworkFirst({ \"cacheName\":\"start-url\", plugins: [{ cacheWillUpdate: async ({ request, response, event, state }) => { if (response && response.type === 'opaqueredirect') { return new Response(response.body, { status: 200, statusText: 'OK', headers: response.headers }) } return response } }] }), 'GET');\nworkbox_routing_registerRoute(/^https:\\/\\/fonts\\.(?:gstatic)\\.com\\/.*/i, new workbox_strategies_CacheFirst({ \"cacheName\":\"google-fonts-webfonts\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: ******** })] }), 'GET');\nworkbox_routing_registerRoute(/^https:\\/\\/fonts\\.(?:googleapis)\\.com\\/.*/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"google-fonts-stylesheets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"static-font-assets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"static-image-assets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\/_next\\/image\\?url=.+$/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"next-image\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:mp3|wav|ogg)$/i, new workbox_strategies_CacheFirst({ \"cacheName\":\"static-audio-assets\", plugins: [new workbox_range_requests_RangeRequestsPlugin(), new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:mp4)$/i, new workbox_strategies_CacheFirst({ \"cacheName\":\"static-video-assets\", plugins: [new workbox_range_requests_RangeRequestsPlugin(), new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:js)$/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"static-js-assets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:css|less)$/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"static-style-assets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\/_next\\/data\\/.+\\/.+\\.json$/i, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"next-data\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:json|xml|csv)$/i, new workbox_strategies_NetworkFirst({ \"cacheName\":\"static-data-assets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(({ url }) => {\n      const isSameOrigin = self.origin === url.origin\n      if (!isSameOrigin) return false\n      const pathname = url.pathname\n      // Exclude /api/auth/callback/* to fix OAuth workflow in Safari without impact other environment\n      // Above route is default for next-auth, you may need to change it if your OAuth workflow has a different callback route\n      // Issue: https://github.com/shadowwalker/next-pwa/issues/131#issuecomment-*********\n      if (pathname.startsWith('/api/auth/')) return false\n      if (pathname.startsWith('/api/')) return true\n      return false\n    }, new workbox_strategies_NetworkFirst({ \"cacheName\":\"apis\",\"networkTimeoutSeconds\":10, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 16, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(({ url }) => {\n      const isSameOrigin = self.origin === url.origin\n      if (!isSameOrigin) return false\n      const pathname = url.pathname\n      if (pathname.startsWith('/api/')) return false\n      return true\n    }, new workbox_strategies_NetworkFirst({ \"cacheName\":\"others\",\"networkTimeoutSeconds\":10, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(({ url }) => {\n      const isSameOrigin = self.origin === url.origin\n      return !isSameOrigin\n    }, new workbox_strategies_NetworkFirst({ \"cacheName\":\"cross-origin\",\"networkTimeoutSeconds\":10, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 3600 })] }), 'GET');\n\n\n\n\n"], "names": ["importScripts", "self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "ignoreURLParametersMatching", "workbox_precaching_cleanupOutdatedCaches", "workbox_routing_registerRoute", "workbox_strategies_NetworkFirst", "cacheName", "plugins", "cacheWillUpdate", "async", "request", "response", "event", "state", "type", "Response", "body", "status", "statusText", "headers", "workbox_strategies_CacheFirst", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds", "workbox_strategies_StaleWhileRevalidate", "workbox_range_requests_RangeRequestsPlugin", "origin", "pathname", "startsWith", "networkTimeoutSeconds"], "mappings": "0nBAqBAA,gBAUAC,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,iCACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,8CACPC,SAAY,oBAEd,CACED,IAAO,kDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,oDACPC,SAAY,yBAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,oDACPC,SAAY,yBAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,8CACPC,SAAY,oBAEd,CACED,IAAO,kDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,oDACPC,SAAY,oBAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,yBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oBAEd,CACED,IAAO,mDACPC,SAAY,oCAEd,CACED,IAAO,+DACPC,SAAY,yBAEd,CACED,IAAO,8EACPC,SAAY,yBAEd,CACED,IAAO,qDACPC,SAAY,yBAEd,CACED,IAAO,yDACPC,SAAY,oCAEd,CACED,IAAO,sDACPC,SAAY,yBAEd,CACED,IAAO,0DACPC,SAAY,oCAEd,CACED,IAAO,0DACPC,SAAY,yBAEd,CACED,IAAO,8DACPC,SAAY,oCAEd,CACED,IAAO,yDACPC,SAAY,yBAEd,CACED,IAAO,6DACPC,SAAY,oCAEd,CACED,IAAO,oDACPC,SAAY,yBAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,4DACPC,SAAY,yBAEd,CACED,IAAO,gEACPC,SAAY,oCAEd,CACED,IAAO,mEACPC,SAAY,yBAEd,CACED,IAAO,4DACPC,SAAY,yBAEd,CACED,IAAO,gEACPC,SAAY,oCAEd,CACED,IAAO,iEACPC,SAAY,yBAEd,CACED,IAAO,qEACPC,SAAY,yBAEd,CACED,IAAO,oDACPC,SAAY,yBAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,qDACPC,SAAY,yBAEd,CACED,IAAO,yDACPC,SAAY,oCAEd,CACED,IAAO,gDACPC,SAAY,yBAEd,CACED,IAAO,oDACPC,SAAY,oCAEd,CACED,IAAO,oDACPC,SAAY,yBAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,sDACPC,SAAY,yBAEd,CACED,IAAO,0DACPC,SAAY,oCAEd,CACED,IAAO,wDACPC,SAAY,yBAEd,CACED,IAAO,4DACPC,SAAY,oCAEd,CACED,IAAO,qDACPC,SAAY,oCAEd,CACED,IAAO,mDACPC,SAAY,yBAEd,CACED,IAAO,uDACPC,SAAY,oCAEd,CACED,IAAO,yCACPC,SAAY,oBAEd,CACED,IAAO,6CACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,iDACPC,SAAY,oCAEd,CACED,IAAO,+CACPC,SAAY,oCAEd,CACED,IAAO,iDACPC,SAAY,oCAEd,CACED,IAAO,wDACPC,SAAY,oCAEd,CACED,IAAO,sDACPC,SAAY,oCAEd,CACED,IAAO,4BACPC,SAAY,oCAEd,CACED,IAAO,4BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,oCAEd,CACED,IAAO,wBACPC,SAAY,oCAEd,CACED,IAAO,wBACPC,SAAY,oCAEd,CACED,IAAO,wBACPC,SAAY,oCAEd,CACED,IAAO,wBACPC,SAAY,oCAEd,CACED,IAAO,8BACPC,SAAY,oCAEd,CACED,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,qBACPC,SAAY,oCAEd,CACED,IAAO,qBACPC,SAAY,oCAEd,CACED,IAAO,qBACPC,SAAY,oCAEd,CACED,IAAO,iBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,oCAEd,CACED,IAAO,qBACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,qCAEb,CACDC,4BAA+B,KAEjCC,EAAAA,wBAIAC,EAAAA,cAA8B,IAAK,IAAIC,eAAgC,CAAEC,UAAY,YAAaC,QAAS,CAAC,CAAEC,gBAAiBC,OAASC,UAASC,WAAUC,QAAOC,WAAkBF,GAA8B,mBAAlBA,EAASG,KAAoC,IAAIC,SAASJ,EAASK,KAAM,CAAEC,OAAQ,IAAKC,WAAY,KAAMC,QAASR,EAASQ,UAAoBR,MAAkB,OAClWP,EAAAA,cAA8B,0CAA2C,IAAIgB,aAA8B,CAAEd,UAAY,wBAAyBC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,EAAGC,cAAe,aAAiB,OACrPnB,EAAAA,cAA8B,6CAA8C,IAAIoB,uBAAwC,CAAElB,UAAY,2BAA4BC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,EAAGC,cAAe,YAAe,OACnQnB,EAAAA,cAA8B,8CAA+C,IAAIoB,uBAAwC,CAAElB,UAAY,qBAAsBC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,EAAGC,cAAe,YAAe,OAC9PnB,EAAAA,cAA8B,wCAAyC,IAAIoB,uBAAwC,CAAElB,UAAY,sBAAuBC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OACzPnB,EAAAA,cAA8B,2BAA4B,IAAIoB,uBAAwC,CAAElB,UAAY,aAAcC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OACnOnB,EAAAA,cAA8B,sBAAuB,IAAIgB,aAA8B,CAAEd,UAAY,sBAAuBC,QAAS,CAAC,IAAIkB,sBAA8C,IAAIJ,EAAAA,iBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OAC/QnB,EAAAA,cAA8B,cAAe,IAAIgB,aAA8B,CAAEd,UAAY,sBAAuBC,QAAS,CAAC,IAAIkB,sBAA8C,IAAIJ,EAAAA,iBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OACvQnB,EAAAA,cAA8B,aAAc,IAAIoB,uBAAwC,CAAElB,UAAY,mBAAoBC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OAC3NnB,EAAAA,cAA8B,mBAAoB,IAAIoB,uBAAwC,CAAElB,UAAY,sBAAuBC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OACpOnB,EAAAA,cAA8B,gCAAiC,IAAIoB,uBAAwC,CAAElB,UAAY,YAAaC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OACvOnB,EAAAA,cAA8B,uBAAwB,IAAIC,eAAgC,CAAEC,UAAY,qBAAsBC,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OAC/NnB,EAAAA,eAA8B,EAAGJ,UAE3B,KADqBJ,KAAK8B,SAAW1B,EAAI0B,QACtB,OAAO,EAC1B,MAAMC,EAAW3B,EAAI2B,SAIrB,OAAIA,EAASC,WAAW,iBACpBD,EAASC,WAAW,WAEvB,IAAIvB,EAAAA,aAAgC,CAAEC,UAAY,OAAOuB,sBAAwB,GAAItB,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OAC7LnB,EAAAA,eAA8B,EAAGJ,UAE3B,KADqBJ,KAAK8B,SAAW1B,EAAI0B,QACtB,OAAO,EAE1B,OADiB1B,EAAI2B,SACRC,WAAW,WAEvB,IAAIvB,EAAAA,aAAgC,CAAEC,UAAY,SAASuB,sBAAwB,GAAItB,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OAC/LnB,EAAAA,eAA8B,EAAGJ,WACNJ,KAAK8B,SAAW1B,EAAI0B,SAExC,IAAIrB,EAAAA,aAAgC,CAAEC,UAAY,eAAeuB,sBAAwB,GAAItB,QAAS,CAAC,IAAIc,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,UAAa", "ignoreList": []}