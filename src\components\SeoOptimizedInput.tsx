"use client";

import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AlertCircleIcon } from 'lucide-react';

interface SeoOptimizedInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  maxLength?: number;
  tooltipContent?: string;
  isTextarea?: boolean;
  placeholder?: string;
  id?: string;
}

const SeoOptimizedInput: React.FC<SeoOptimizedInputProps> = ({
  value,
  onChange,
  maxLength,
  tooltipContent,
  isTextarea = false,
  placeholder,
  id,
}) => {
  const InputComponent = isTextarea ? Textarea : Input;
  const currentLength = value.length;

  return (
    <TooltipProvider delayDuration={300}>
      <div className="relative w-full">
        <InputComponent
          id={id}
          value={value}
          onChange={onChange}
          maxLength={maxLength}
          placeholder={placeholder}
          className={`pr-16 ${isTextarea ? 'min-h-[60px]' : ''}`}
        />
        <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-2">
          {maxLength && (
            <span className={`text-xs ${currentLength > maxLength ? 'text-destructive' : 'text-muted-foreground'}`}>
              {currentLength}/{maxLength}
            </span>
          )}
          {tooltipContent && (
            <Tooltip>
              <TooltipTrigger asChild>
                <button type="button" className="text-muted-foreground hover:text-foreground">
                  <AlertCircleIcon className="h-4 w-4" />
                </button>
              </TooltipTrigger>
              <TooltipContent side="top" align="end" className="max-w-xs bg-popover text-popover-foreground p-2 rounded shadow-lg text-sm">
                <p>{tooltipContent}</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default SeoOptimizedInput;
