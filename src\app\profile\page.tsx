
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useRouter } from 'next/navigation';
import { Loader2, Settings, Gem, FileText, PlusCircle, Pencil, Trash2, ExternalLink, ShieldCheck, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import EditTemplateDialog from '@/components/profile/EditTemplateDialog';
import { getTemplates, saveTemplate, updateTemplate, deleteTemplate } from '@/services/templateService';
import type { CreateTemplateData } from '@/types';
import type { MetadataTemplate } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { PLANS, DEFAULT_PLAN_ID, LEMON_SQUEEZY_CUSTOMER_PORTAL_URL } from '@/config/plans';
import Link from 'next/link';
import { format } from 'date-fns';


export default function ProfilePage() {
  const { user, loading: authLoading } = useAuth();
  const { subscription, loadingSubscription, refreshSubscription } = useSubscription();
  const router = useRouter();
  const { toast } = useToast();

  const [templates, setTemplates] = useState<MetadataTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [templateToEdit, setTemplateToEdit] = useState<MetadataTemplate | null>(null);
  const [templateToDelete, setTemplateToDelete] = useState<MetadataTemplate | null>(null);

  useEffect(() => {
    if (!authLoading && !user) {
      router.replace('/login');
    }
  }, [user, authLoading, router]);

  const fetchTemplates = useCallback(async () => {
    if (user?.uid && subscription?.planId === 'pro') {
      setIsLoadingTemplates(true);
      try {
        const userTemplates = await getTemplates(user.uid);
        setTemplates(userTemplates);
      } catch (error) {
        console.error("Failed to load templates:", error);
        toast({ title: "Error", description: "Could not load your templates.", variant: "destructive" });
      } finally {
        setIsLoadingTemplates(false);
      }
    } else {
      setTemplates([]); // Clear templates if not Pro or no user
    }
  }, [user, toast, subscription]);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);


  const handleOpenCreateDialog = () => {
    setTemplateToEdit(null);
    setIsEditDialogOpen(true);
  };

  const handleOpenEditDialog = (template: MetadataTemplate) => {
    setTemplateToEdit(template);
    setIsEditDialogOpen(true);
  };

  const handleOpenDeleteDialog = (template: MetadataTemplate) => {
    setTemplateToDelete(template);
    setIsDeleteDialogOpen(true);
  };

  const handleSaveTemplate = async (templateData: CreateTemplateData, templateId?: string): Promise<MetadataTemplate | null> => {
    if (!user?.uid) {
      toast({ title: "Error", description: "You must be logged in.", variant: "destructive" });
      return null;
    }
    let result: MetadataTemplate | null = null;
    if (templateId) { // Editing existing
      result = await updateTemplate(user.uid, templateId, templateData);
      toast({ title: result ? "Template Updated" : "Update Failed", description: result ? `Template "${result.name}" saved.` : "Could not update template." });
    } else { // Creating new
      result = await saveTemplate(user.uid, templateData);
      toast({ title: result ? "Template Created" : "Creation Failed", description: result ? `Template "${result.name}" created.` : "Could not create template." });
    }
    if (result) {
      fetchTemplates(); // Refresh list
    }
    return result;
  };

  const handleDeleteTemplate = async () => {
    if (!user?.uid || !templateToDelete) {
      toast({ title: "Error", description: "Action cannot be completed.", variant: "destructive" });
      return;
    }
    const success = await deleteTemplate(user.uid, templateToDelete.id);
    toast({ title: success ? "Template Deleted" : "Deletion Failed", description: success ? `Template "${templateToDelete.name}" removed.` : "Could not delete template." });
    if (success) {
      fetchTemplates(); // Refresh list
      setIsDeleteDialogOpen(false);
      setTemplateToDelete(null);
    }
  };

  const isLoading = authLoading || loadingSubscription;
  const currentPlan = PLANS.find(p => p.id === (subscription?.planId || DEFAULT_PLAN_ID)) || PLANS.find(p => p.id === DEFAULT_PLAN_ID)!;

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background py-12 px-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading profile...</p>
      </div>
    );
  }

  if (!user) {
    // Should be redirected by useEffect, but this is a fallback
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-background py-12 px-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Redirecting...</p>
      </div>
    );
  }
  
  const formatDate = (timestamp: any): string => {
    if (!timestamp || !timestamp.toDate) return 'N/A';
    try {
      return format(timestamp.toDate(), 'PPpp');
    } catch (e) {
      return 'Invalid Date';
    }
  };


  return (
    <div className="container mx-auto py-8 px-4 sm:px-6 lg:px-8 max-w-4xl">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-primary flex items-center">
          <Settings className="mr-3 h-8 w-8" />
          User Profile & Settings
        </h1>
        <p className="text-muted-foreground mt-1">Manage your account, subscription, and metadata templates.</p>
      </header>

      <Tabs defaultValue="subscription" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="subscription">Subscription Details</TabsTrigger>
          <TabsTrigger value="templates">Manage Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="subscription">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {currentPlan.id === 'pro' ? <Gem className="mr-2 h-5 w-5 text-accent" /> : <ShieldCheck className="mr-2 h-5 w-5 text-green-500" />}
                Your Current Plan: {currentPlan.name}
              </CardTitle>
              <CardDescription>
                {subscription?.status === 'active' && subscription?.currentPeriodEnd 
                  ? `Renews on ${formatDate(subscription.currentPeriodEnd)}.`
                  : subscription?.status === 'active' && subscription?.cancelAtPeriodEnd
                  ? `Active until ${formatDate(subscription.currentPeriodEnd)}, then cancels.`
                  : subscription?.status ? `Status: ${subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}.` : 'Loading status...'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Daily AI Suggestions Usage (Resets Daily)</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 text-sm">
                    <p>Keyword Suggestions: 
                        <span className="font-semibold text-foreground">
                            {subscription?.dailyUsage.keywordSuggestions.count || 0} / {currentPlan.limits.keywordSuggestionsPerDay}
                        </span> used
                    </p>
                    <p>Subject Suggestions: 
                        <span className="font-semibold text-foreground">
                            {subscription?.dailyUsage.subjectSuggestions.count || 0} / {currentPlan.limits.subjectSuggestionsPerDay}
                        </span> used
                    </p>
                </div>
              </div>
               <div>
                <h3 className="text-sm font-medium text-muted-foreground">Purchased Addon Packs Remaining</h3>
                 <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 text-sm">
                    <p>Keyword Suggestions: <span className="font-semibold text-foreground">{subscription?.addons.keywordSuggestions || 0}</span></p>
                    <p>Subject Suggestions: <span className="font-semibold text-foreground">{subscription?.addons.subjectSuggestions || 0}</span></p>
                 </div>
              </div>
               {subscription?.cancelAtPeriodEnd && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-700 text-sm flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 shrink-0"/>
                    Your subscription is set to cancel at the end of the current billing period ({formatDate(subscription.currentPeriodEnd)}). You will retain access to Pro features until then.
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-2 items-stretch sm:items-center">
              <Button asChild className="w-full sm:w-auto">
                <Link href="/pricing">
                  {currentPlan.id === 'free' ? 'Upgrade to Pro' : 'View/Change Plans'}
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full sm:w-auto">
                <a href={LEMON_SQUEEZY_CUSTOMER_PORTAL_URL} target="_blank" rel="noopener noreferrer">
                  Manage Billing <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          {subscription?.planId === 'pro' ? (
            <Card>
              <CardHeader className="flex flex-row justify-between items-center">
                <div>
                  <CardTitle>Your Metadata Templates</CardTitle>
                  <CardDescription>Create, edit, or delete your reusable metadata templates.</CardDescription>
                </div>
                <Button onClick={handleOpenCreateDialog} size="sm">
                  <PlusCircle className="mr-2 h-4 w-4" /> Create New
                </Button>
              </CardHeader>
              <CardContent>
                {isLoadingTemplates ? (
                  <div className="flex justify-center py-8"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>
                ) : templates.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead className="hidden sm:table-cell">Title Preview</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {templates.map(template => (
                        <TableRow key={template.id}>
                          <TableCell className="font-medium">{template.name}</TableCell>
                          <TableCell className="hidden sm:table-cell text-muted-foreground truncate max-w-xs">{template.title || '-'}</TableCell>
                          <TableCell className="text-right space-x-2">
                            <Button variant="ghost" size="icon" onClick={() => handleOpenEditDialog(template)} title="Edit template">
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => handleOpenDeleteDialog(template)} title="Delete template">
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    You haven&apos;t created any templates yet. Click &quot;Create New&quot; to get started.
                  </p>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card className="text-center">
              <CardHeader>
                <CardTitle className="flex items-center justify-center">
                    <Gem className="mr-2 h-6 w-6 text-accent" />
                    Unlock Template Management with Pro
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Saving and managing metadata templates is a Pro feature. Upgrade your plan to streamline your workflow by creating reusable templates.
                </p>
              </CardContent>
              <CardFooter className="justify-center">
                 <Button asChild>
                    <Link href="/pricing">Upgrade to Pro</Link>
                 </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {isEditDialogOpen && (
        <EditTemplateDialog
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          onSave={handleSaveTemplate}
          existingTemplate={templateToEdit}
        />
      )}

      {isDeleteDialogOpen && templateToDelete && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Template: {templateToDelete.name}?</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this template? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteTemplate} className="bg-destructive hover:bg-destructive/90">
                Yes, Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}
