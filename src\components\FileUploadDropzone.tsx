
"use client";

import React, { useCallback } from 'react';
import { useDropzone, type Accept } from 'react-dropzone';
import { UploadCloudIcon } from 'lucide-react';
import type { PdfFile, Metadata } from '@/types';
import { Button } from '@/components/ui/button';
import * as pdfjsLib from 'pdfjs-dist';
import type { TextItem, DocumentInitParameters } from 'pdfjs-dist/types/src/display/api';
import { useToast } from "@/hooks/use-toast";

// Set worker source for pdf.js
if (typeof window !== 'undefined') { // Run only in browser
   pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

interface FileUploadDropzoneProps {
  onFilesAdded: (files: PdfFile[]) => void;
}

const FileUploadDropzone: React.FC<FileUploadDropzoneProps> = ({ onFilesAdded }) => {
  const { toast } = useToast();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newPdfFilesPromises = acceptedFiles
      .filter(file => file.type === 'application/pdf' || file.name.endsWith('.pdf'))
      .map(async (file): Promise<PdfFile | null> => {
        let fullText = `Error: Could not read text content from ${file.name}.`;
        let initialMetadata: Metadata = {
          title: file.name.replace(/\.pdf$/i, ''),
          author: '',
          subject: '',
          keywords: '',
          producer: '',
          creatorTool: '',
        };
        let originalFileBuffer: ArrayBuffer | undefined = undefined;

        try {
          const arrayBuffer = await file.arrayBuffer();
          // Store a copy of the buffer for pdf-lib to use later, to avoid detached buffer issues
          originalFileBuffer = arrayBuffer.slice(0);

          const pdfJsBuffer = arrayBuffer.slice(0);
          const pdfDoc = await pdfjsLib.getDocument({ data: pdfJsBuffer } as DocumentInitParameters).promise;
          
          let extractedTextContent = '';
          for (let i = 1; i <= pdfDoc.numPages; i++) {
            const page = await pdfDoc.getPage(i);
            const textContent = await page.getTextContent();
            extractedTextContent += textContent.items.map((item) => (item as TextItem).str).join(' ') + '\n';
          }
          fullText = extractedTextContent.trim();
          if (!fullText) {
            fullText = `No text content found in ${file.name}.`;
          }

          const { info: pdfInfo } = await pdfDoc.getMetadata();

          initialMetadata.title = pdfInfo?.Title || file.name.replace(/\.pdf$/i, '');
          initialMetadata.author = pdfInfo?.Author || '';
          initialMetadata.subject = pdfInfo?.Subject || '';
          initialMetadata.keywords = (pdfInfo?.Keywords as string) || '';
          initialMetadata.producer = pdfInfo?.Producer || '';
          initialMetadata.creatorTool = pdfInfo?.Creator || '';


        } catch (error) {
          console.error("Error parsing PDF or fetching metadata:", file.name, error);
          fullText = `Error processing PDF ${file.name}. Check console for details.`;
           toast({
            variant: "destructive",
            title: "PDF Processing Error",
            description: `Failed to process ${file.name}. It might be corrupted or password-protected.`,
          });
          return null;
        }
        
        return {
          id: crypto.randomUUID(),
          name: file.name,
          originalNameOnUpload: file.name,
          metadata: initialMetadata,
          documentContentPreview: fullText,
          originalFileBuffer: originalFileBuffer,
        };
      });

    const newPdfFiles = (await Promise.all(newPdfFilesPromises)).filter(f => f !== null) as PdfFile[];
    if (newPdfFiles.length > 0) {
      onFilesAdded(newPdfFiles);
    }
  }, [onFilesAdded, toast]);
  
  const acceptOptions: Accept = { 'application/pdf': ['.pdf'] };

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: acceptOptions,
    noClick: true,
    noKeyboard: true,
  });

  return (
    <div
      {...getRootProps()}
      className={`w-full max-w-2xl p-8 border-2 border-dashed rounded-lg cursor-pointer transition-colors
        ${isDragActive ? 'border-primary bg-primary/10' : 'border-border hover:border-primary/70'}
        flex flex-col items-center justify-center text-center bg-card shadow-sm`}
    >
      <input {...getInputProps()} />
      <UploadCloudIcon className={`w-16 h-16 mb-4 ${isDragActive ? 'text-primary' : 'text-muted-foreground'}`} />
      {isDragActive ? (
        <p className="text-lg font-semibold text-primary">Drop the PDF files here...</p>
      ) : (
        <>
          <p className="text-lg font-semibold text-foreground">Drag & drop some PDF files here, or click to select files</p>
          <p className="text-sm text-muted-foreground mt-1">Supports .pdf files only</p>
        </>
      )}
      <Button
        variant="outline"
        size="sm"
        className="mt-4"
        onClick={(e) => {
          e.stopPropagation();
          open();
        }}
      >
        Select Files
      </Button>
    </div>
  );
};

export default FileUploadDropzone;

