
"use client";

import React, { useCallback } from 'react';
import { useDropzone, type Accept } from 'react-dropzone';
import { UploadCloudIcon, AlertTriangle } from 'lucide-react';
import type { PdfFile, Metadata } from '@/types';
import { But<PERSON> } from '@/components/ui/button';
import * as pdfjsLib from 'pdfjs-dist';
import type { TextItem, DocumentInitParameters } from 'pdfjs-dist/types/src/display/api';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { PLANS, DEFAULT_PLAN_ID } from '@/config/plans';

// Set worker source for pdf.js
if (typeof window !== 'undefined') { // Run only in browser
   pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

interface FileUploadDropzoneProps {
  onFilesAdded: (files: PdfFile[]) => void;
}

// Smart file size limits based on plan and browser capabilities
const FILE_SIZE_LIMITS = {
  free: 100 * 1024 * 1024,  // 100MB - handles most real-world PDFs
  pro: 200 * 1024 * 1024,   // 200MB - handles large technical documents
};

const FILE_SIZE_WARNING = 50 * 1024 * 1024; // 50MB - warn but don't block

const FileUploadDropzone: React.FC<FileUploadDropzoneProps> = ({ onFilesAdded }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { subscription } = useSubscription();

  // Get current plan limits
  const getCurrentPlan = () => {
    const planId = subscription?.planId || DEFAULT_PLAN_ID;
    return PLANS.find(plan => plan.id === planId) || PLANS[0];
  };

  const validateFile = (file: File): { valid: boolean; warning?: string; error?: string } => {
    // Check file type
    if (!file.type.includes('pdf') && !file.name.toLowerCase().endsWith('.pdf')) {
      return { valid: false, error: 'Only PDF files are supported.' };
    }

    const currentPlan = getCurrentPlan();
    const maxSize = FILE_SIZE_LIMITS[currentPlan.id as keyof typeof FILE_SIZE_LIMITS] || FILE_SIZE_LIMITS.free;

    // Hard limit check
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      const fileSizeMB = Math.round(file.size / (1024 * 1024));
      return {
        valid: false,
        error: `File "${file.name}" is ${fileSizeMB}MB, which exceeds the ${maxSizeMB}MB limit for ${currentPlan.name} plan. Consider upgrading or using a smaller file.`
      };
    }

    // Warning for large files
    if (file.size > FILE_SIZE_WARNING) {
      const fileSizeMB = Math.round(file.size / (1024 * 1024));
      return {
        valid: true,
        warning: `"${file.name}" is ${fileSizeMB}MB. Large files may take longer to process and could impact browser performance.`
      };
    }

    return { valid: true };
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    // Validate files first
    const validFiles: File[] = [];
    const warnings: string[] = [];

    for (const file of acceptedFiles) {
      const validation = validateFile(file);

      if (!validation.valid) {
        toast({
          title: "File Upload Error",
          description: validation.error,
          variant: "destructive",
          duration: 8000,
        });
        continue;
      }

      if (validation.warning) {
        warnings.push(validation.warning);
      }

      validFiles.push(file);
    }

    // Show warnings for large files
    if (warnings.length > 0) {
      toast({
        title: "Large File Warning",
        description: warnings.join(' '),
        variant: "default",
        duration: 10000,
      });
    }

    if (validFiles.length === 0) {
      return;
    }

    const newPdfFilesPromises = validFiles
      .map(async (file): Promise<PdfFile | null> => {
        let fullText = `Error: Could not read text content from ${file.name}.`;
        let initialMetadata: Metadata = {
          title: file.name.replace(/\.pdf$/i, ''),
          author: '',
          subject: '',
          keywords: '',
          producer: '',
          creatorTool: '',
        };
        let originalFileBuffer: ArrayBuffer | undefined = undefined;

        try {
          const arrayBuffer = await file.arrayBuffer();
          // Store a copy of the buffer for pdf-lib to use later, to avoid detached buffer issues
          originalFileBuffer = arrayBuffer.slice(0);

          const pdfJsBuffer = arrayBuffer.slice(0);
          const pdfDoc = await pdfjsLib.getDocument({ data: pdfJsBuffer } as DocumentInitParameters).promise;
          
          let extractedTextContent = '';
          for (let i = 1; i <= pdfDoc.numPages; i++) {
            const page = await pdfDoc.getPage(i);
            const textContent = await page.getTextContent();
            extractedTextContent += textContent.items.map((item) => (item as TextItem).str).join(' ') + '\n';
          }
          fullText = extractedTextContent.trim();
          if (!fullText) {
            fullText = `No text content found in ${file.name}.`;
          }

          const { info: pdfInfo } = await pdfDoc.getMetadata();

          initialMetadata.title = pdfInfo?.Title || file.name.replace(/\.pdf$/i, '');
          initialMetadata.author = pdfInfo?.Author || '';
          initialMetadata.subject = pdfInfo?.Subject || '';
          initialMetadata.keywords = (pdfInfo?.Keywords as string) || '';
          initialMetadata.producer = pdfInfo?.Producer || '';
          initialMetadata.creatorTool = pdfInfo?.Creator || '';


        } catch (error) {
          console.error("Error parsing PDF or fetching metadata:", file.name, error);
          fullText = `Error processing PDF ${file.name}. Check console for details.`;
           toast({
            variant: "destructive",
            title: "PDF Processing Error",
            description: `Failed to process ${file.name}. It might be corrupted or password-protected.`,
          });
          return null;
        }
        
        return {
          id: crypto.randomUUID(),
          name: file.name,
          originalNameOnUpload: file.name,
          metadata: initialMetadata,
          documentContentPreview: fullText,
          originalFileBuffer: originalFileBuffer,
        };
      });

    const newPdfFiles = (await Promise.all(newPdfFilesPromises)).filter(f => f !== null) as PdfFile[];
    if (newPdfFiles.length > 0) {
      onFilesAdded(newPdfFiles);
    }
  }, [onFilesAdded, toast, subscription]);
  
  const acceptOptions: Accept = { 'application/pdf': ['.pdf'] };

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: acceptOptions,
    noClick: true,
    noKeyboard: true,
  });

  return (
    <div
      {...getRootProps()}
      className={`w-full max-w-2xl p-8 border-2 border-dashed rounded-lg cursor-pointer transition-colors
        ${isDragActive ? 'border-primary bg-primary/10' : 'border-border hover:border-primary/70'}
        flex flex-col items-center justify-center text-center bg-card shadow-sm`}
    >
      <input {...getInputProps()} />
      <UploadCloudIcon className={`w-16 h-16 mb-4 ${isDragActive ? 'text-primary' : 'text-muted-foreground'}`} />
      {isDragActive ? (
        <p className="text-lg font-semibold text-primary">Drop the PDF files here...</p>
      ) : (
        <>
          <p className="text-lg font-semibold text-foreground">Drag & drop some PDF files here, or click to select files</p>
          <div className="text-sm text-muted-foreground mt-1 space-y-1">
            <p>Supports .pdf files only</p>
            <p>
              Maximum file size: {Math.round((FILE_SIZE_LIMITS[getCurrentPlan().id as keyof typeof FILE_SIZE_LIMITS] || FILE_SIZE_LIMITS.free) / (1024 * 1024))}MB
              {getCurrentPlan().id === 'free' && (
                <span className="text-primary ml-1">(upgrade to Pro for 200MB)</span>
              )}
            </p>
          </div>
        </>
      )}
      <Button
        variant="outline"
        size="sm"
        className="mt-4"
        onClick={(e) => {
          e.stopPropagation();
          open();
        }}
      >
        Select Files
      </Button>
    </div>
  );
};

export default FileUploadDropzone;

