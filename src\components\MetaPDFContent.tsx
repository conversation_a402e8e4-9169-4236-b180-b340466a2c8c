
"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import type { PdfFile, Metadata, MetadataTemplate } from '@/types';
import FileUploadDropzone from '@/components/FileUploadDropzone';
import MetadataControls from '@/components/MetadataControls';
import { useToast } from "@/hooks/use-toast";
import { PDFDocument } from 'pdf-lib';
import { Card } from '@/components/ui/card';
import { FileIcon, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { saveTemplate as saveTemplateToFirestore, getTemplates as getTemplatesFromFirestore, type CreateTemplateData } from '@/services/templateService';
import dynamic from 'next/dynamic';
import FileOperationErrorBoundary from '@/components/FileOperationErrorBoundary';
import AIOperationErrorBoundary from '@/components/AIOperationErrorBoundary';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import Image from 'next/image';
import { PLANS, DEFAULT_PLAN_ID } from '@/config/plans';
import { Button } from '@/components/ui/button';
import Link from 'next/link';


const BulkEditDialog = dynamic(() => import('@/components/BulkEditDialog'), {
  loading: () => <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>,
});
const ExtractMetadataDialog = dynamic(() => import('@/components/ExtractMetadataDialog'), {
  loading: () => <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>,
});
const ApplyTemplateDialog = dynamic(() => import('@/components/ApplyTemplateDialog'), {
  loading: () => <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>,
});
const RenameFilesDialog = dynamic(() => import('@/components/RenameFilesDialog'), {
  loading: () => <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>,
});
const ConfirmClearDialog = dynamic(() => import('@/components/ConfirmClearDialog'), {
  loading: () => <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>,
});

const MetadataDisplayTable = dynamic(() => import('@/components/MetadataDisplayTable'), {
  loading: () => <div className="w-full max-w-6xl mt-10 flex justify-center items-center p-8"><Loader2 className="h-10 w-10 animate-spin text-primary" /></div>,
  ssr: false,
});


const sanitizeFilenamePart = (part: string): string => {
  if (typeof part !== 'string') return '';
  return part.replace(/[\\/:*?"<>|]/g, '_');
};


export default function MetaPDFContent() {
  const [files, setFiles] = useState<PdfFile[]>([]);
  const [selectedFileIds, setSelectedFileIds] = useState<Set<string>>(new Set());
  const [isSeoView, setIsSeoView] = useState<boolean>(false);
  const [isBulkEditModalOpen, setIsBulkEditModalOpen] = useState<boolean>(false);
  const [isExtractModalOpen, setIsExtractModalOpen] = useState<boolean>(false);
  const [isApplyTemplateModalOpen, setIsApplyTemplateModalOpen] = useState<boolean>(false);
  const [isRenameModalOpen, setIsRenameModalOpen] = useState<boolean>(false);
  const [isConfirmClearOpen, setIsConfirmClearOpen] = useState<boolean>(false);
  const [templates, setTemplates] = useState<MetadataTemplate[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState<boolean>(false);
  const [openFileAccordionItems, setOpenFileAccordionItems] = useState<string[]>([]);
  const { toast } = useToast();
  const { user, loading: authLoading } = useAuth();
  const { subscription, loadingSubscription } = useSubscription();

  useEffect(() => {
    if (authLoading) {
      return;
    }

    if (user) {
      setIsLoadingTemplates(true);
      getTemplatesFromFirestore(user.uid)
        .then(userTemplates => {
          setTemplates(userTemplates);
        })
        .catch(error => {
          console.error("Failed to load templates from Firestore:", error);
          setTimeout(() => {
            toast({
              title: "Error Loading Templates",
              description: "Could not load your saved metadata templates.",
              variant: "destructive",
            });
          }, 0);
        })
        .finally(() => {
          setIsLoadingTemplates(false);
        });
    } else {
      setTemplates([]);
      setIsLoadingTemplates(false);
    }
  }, [user, authLoading, toast]);

  useEffect(() => {
    // Initialize or update open accordion items when files change
    setOpenFileAccordionItems(files.map(f => f.id));
  }, [files]);

  const handleExpandAllFiles = useCallback(() => {
    setOpenFileAccordionItems(files.map(f => f.id));
  }, [files]);

  const handleCollapseAllFiles = useCallback(() => {
    setOpenFileAccordionItems([]);
  }, []);


  const handleCreateTemplate = useCallback(async (newTemplateCoreData: CreateTemplateData): Promise<MetadataTemplate | null> => {
    if (authLoading) {
      setTimeout(() => {
        toast({ title: "Authentication State Pending", description: "Please wait a moment and try again.", variant: "default" });
      }, 0);
      return null;
    }
    if (!user || !user.uid) {
      console.error("Attempted to create template without a logged-in user or user.uid. User:", user, "AuthLoading:", authLoading);
      setTimeout(() => {
        toast({ title: "Authentication Required", description: "You must be logged in with a valid UID to create templates.", variant: "destructive" });
      }, 0);
      return null;
    }

    const createdTemplate = await saveTemplateToFirestore(user.uid, newTemplateCoreData);

    if (createdTemplate) {
      setTemplates(prevTemplates => [...prevTemplates, createdTemplate]);
      setTimeout(() => {
        toast({
          title: "Template Created",
          description: `Template "${createdTemplate.name}" has been saved to your account.`,
        });
      }, 0);
      return createdTemplate;
    } else {
      setTimeout(() => {
        toast({
          title: "Failed to Create Template",
          description: "Could not save the new template. Check browser console, Firestore rules, and ensure you are logged in.",
          variant: "destructive"
        });
      }, 0);
      return null;
    }
  }, [user, authLoading, toast]);

  const handleApplyTemplate = useCallback((template: MetadataTemplate) => {
    setFiles(prevFiles =>
      prevFiles.map(file => {
        if (selectedFileIds.has(file.id)) {
          const updatedMetadata: Partial<Metadata> = {};
          if (template.title !== undefined && template.title.trim() !== "") updatedMetadata.title = template.title;
          if (template.author !== undefined && template.author.trim() !== "") updatedMetadata.author = template.author;
          if (template.subject !== undefined && template.subject.trim() !== "") updatedMetadata.subject = template.subject;
          if (template.keywords !== undefined && template.keywords.trim() !== "") updatedMetadata.keywords = template.keywords;
          if (template.producer !== undefined && template.producer.trim() !== "") updatedMetadata.producer = template.producer;
          if (template.creatorTool !== undefined && template.creatorTool.trim() !== "") updatedMetadata.creatorTool = template.creatorTool;

          return { ...file, metadata: { ...file.metadata, ...updatedMetadata } };
        }
        return file;
      })
    );
    setTimeout(() => {
      toast({
        title: "Template Applied",
        description: `Template "${template.name}" applied to ${selectedFileIds.size} file(s).`,
      });
    }, 0);
  }, [selectedFileIds, toast]);


  const handleFilesAdded = useCallback((newFiles: PdfFile[]) => {
    setFiles(prevFiles => {
      if (loadingSubscription) {
        setTimeout(() => {
          toast({
            title: "Processing Upload...",
            description: "Checking subscription details. Please wait a moment.",
            variant: "default",
          });
        }, 0);
        return prevFiles;
      }

      const currentPlanId = subscription?.planId || DEFAULT_PLAN_ID;
      const planDetails = PLANS.find(p => p.id === currentPlanId);
      const maxFilesLimit = planDetails?.limits.maxFiles;
      let processedNewFiles = [...newFiles];

      if (subscription?.planId === 'free') {
        processedNewFiles = processedNewFiles.map(file => {
          const newMetadata = { ...file.metadata };
          if (!newMetadata.producer || newMetadata.producer.trim() === "") {
            newMetadata.producer = "MetaPDF";
          }
          if (!newMetadata.creatorTool || newMetadata.creatorTool.trim() === "") {
            newMetadata.creatorTool = "MetaPDF";
          }
          return { ...file, metadata: newMetadata };
        });
      }


      if (maxFilesLimit !== undefined) {
        if (prevFiles.length >= maxFilesLimit) {
          setTimeout(() => {
            toast({
              title: "File Limit Reached",
              description: `You've reached the maximum of ${maxFilesLimit} files for the ${planDetails?.name} plan.`,
              variant: "destructive",
              action: (
                <Button variant="outline" size="sm" asChild>
                  <Link href="/pricing">Upgrade Plan</Link>
                </Button>
              ),
              duration: 10000,
            });
          }, 0);
          return prevFiles;
        }

        const currentFileNames = new Set(prevFiles.map(f => f.name));
        let uniqueIncomingFiles = processedNewFiles.filter(nf => !currentFileNames.has(nf.name));
        const duplicateCount = processedNewFiles.length - uniqueIncomingFiles.length;

        const remainingSlots = maxFilesLimit - prevFiles.length;
        let filesSkippedDueToLimit = 0;

        if (uniqueIncomingFiles.length > remainingSlots) {
          filesSkippedDueToLimit = uniqueIncomingFiles.length - remainingSlots;
          uniqueIncomingFiles = uniqueIncomingFiles.slice(0, remainingSlots);
        }

        const updatedFiles = [...prevFiles, ...uniqueIncomingFiles];

        if (uniqueIncomingFiles.length > 0) {
          setTimeout(() => {
            toast({
              title: "Files Added",
              description: `${uniqueIncomingFiles.length} PDF file(s) ready for editing.`,
            });
          }, 0);
        }
        if (duplicateCount > 0) {
          setTimeout(() => {
            toast({
              title: "Duplicate files skipped",
              description: `${duplicateCount} file(s) were already in the list.`,
              variant: "default",
            });
          }, 0);
        }
        if (filesSkippedDueToLimit > 0) {
          setTimeout(() => {
            toast({
              title: "File Limit Hit",
              description: `${filesSkippedDueToLimit} file(s) were not added as it would exceed the ${maxFilesLimit} file limit for your plan.`,
              variant: "default",
               action: (
                <Button variant="outline" size="sm" asChild>
                  <Link href="/pricing">Upgrade Plan</Link>
                </Button>
              ),
              duration: 10000,
            });
          }, 0);
        }
        return updatedFiles;
      } else {
        const currentFileNames = new Set(prevFiles.map(f => f.name));
        const uniqueIncomingFiles = processedNewFiles.filter(nf => !currentFileNames.has(nf.name));
        const duplicateCount = processedNewFiles.length - uniqueIncomingFiles.length;
        const updatedFiles = [...prevFiles, ...uniqueIncomingFiles];

        if (uniqueIncomingFiles.length > 0) {
          setTimeout(() => {
            toast({
              title: "Files Added",
              description: `${uniqueIncomingFiles.length} PDF file(s) ready for editing.`,
            });
          }, 0);
        }
        if (duplicateCount > 0) {
          setTimeout(() => {
            toast({
              title: "Duplicate files skipped",
              description: `${duplicateCount} file(s) were already in the list.`,
              variant: "default",
            });
          }, 0);
        }
        return updatedFiles;
      }
    });
  }, [toast, subscription, loadingSubscription]);

  const updateFileMetadata = useCallback((fileId: string, updatedMetadata: Partial<Metadata>, updatedFields?: Partial<Pick<PdfFile, 'isLoadingKeywords' | 'suggestedKeywords' | 'isLoadingSubject'>>) => {
    setFiles(prevFiles =>
      prevFiles.map(file =>
        file.id === fileId ? { ...file, metadata: { ...file.metadata, ...updatedMetadata }, ...updatedFields } : file
      )
    );
  }, []);

  const handleSelectFile = useCallback((fileId: string, isSelected: boolean) => {
    setSelectedFileIds(prevSelected => {
      const nextSelected = new Set(prevSelected);
      if (isSelected) {
        nextSelected.add(fileId);
      } else {
        nextSelected.delete(fileId);
      }
      return nextSelected;
    });
  }, []);

  const handleToggleSeoView = useCallback((checked: boolean) => {
    setIsSeoView(checked);
  }, []);

  const handleSaveAll = useCallback(async () => {
    if (files.length === 0) {
      setTimeout(() => {
        toast({
          title: "No Files to Save",
          description: "Please upload some PDF files first.",
        });
      }, 0);
      return;
    }

    setTimeout(() => {
      toast({
        title: "Processing Files...",
        description: `Preparing ${files.length} PDF(s) for download. This may take a moment. Please don't navigate away.`,
        duration: files.length * 2000,
      });
    }, 0);

    let successCount = 0;
    let errorCount = 0;

    for (const file of files) {
      if (!file.originalFileBuffer) {
        console.error(`Original file buffer missing for ${file.name}`);
        setTimeout(() => {
          toast({
            title: `Error Saving ${file.name}`,
            description: "Original file data is missing. Please try re-uploading.",
            variant: "destructive",
          });
        }, 0);
        errorCount++;
        continue;
      }

      try {
        const pdfDoc = await PDFDocument.load(file.originalFileBuffer.slice(0));

        pdfDoc.setTitle(file.metadata.title || '');
        pdfDoc.setAuthor(file.metadata.author || '');
        pdfDoc.setSubject(file.metadata.subject || '');

        const keywordsArray = (file.metadata.keywords || '').split(',').map(k => k.trim()).filter(Boolean);
        pdfDoc.setKeywords(keywordsArray);

        pdfDoc.setProducer(file.metadata.producer || '');
        pdfDoc.setCreator(file.metadata.creatorTool || '');
        pdfDoc.setModificationDate(new Date());

        const pdfBytes = await pdfDoc.save();

        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);

        link.download = file.name;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
        successCount++;
      } catch (error) {
        console.error(`Failed to save ${file.name}:`, error);
        setTimeout(() => {
          toast({
            title: `Error Saving ${file.name}`,
            description: `Could not update metadata. ${error instanceof Error ? error.message : 'Unknown error'}. See console for details.`,
            variant: "destructive",
          });
        }, 0);
        errorCount++;
      }
    }

    if (successCount > 0 && errorCount === 0) {
      setTimeout(() => {
        toast({
          title: "All Files Saved Successfully",
          description: `${successCount} PDF(s) have been updated and downloaded.`,
        });
      }, 0);
    } else if (successCount > 0 && errorCount > 0) {
       setTimeout(() => {
        toast({
          title: "Partial Success",
          description: `${successCount} PDF(s) saved. ${errorCount} failed. Check notifications.`,
          variant: "default",
        });
      }, 0);
    } else if (errorCount > 0 && successCount === 0) {
       setTimeout(() => {
        toast({
          title: "Save Failed",
          description: `All ${errorCount} PDF(s) failed to save. Please check for errors.`,
          variant: "destructive",
        });
      }, 0);
    }

  }, [files, toast]);

  const executeClearAll = useCallback(() => {
    setFiles([]);
    setSelectedFileIds(new Set());
    setTimeout(() => {
      toast({
        title: "All Files Cleared",
        description: "The file list has been emptied.",
      });
    }, 0);
  }, [toast]);

  const handleConfirmClearAll = useCallback(() => {
     if (files.length > 0) {
      setIsConfirmClearOpen(true);
    } else {
       setTimeout(() => {
        toast({
          title: "No Files to Clear",
          description: "The file list is already empty.",
        });
      }, 0);
    }
  }, [files.length, toast]);


  const handleApplyBulkEdit = useCallback((field: keyof Metadata, value: string) => {
    setFiles(prevFiles =>
      prevFiles.map(file =>
        selectedFileIds.has(file.id) ? { ...file, metadata: { ...file.metadata, [field]: value } } : file
      )
    );
    setTimeout(() => {
      toast({
        title: "Bulk Edit Applied",
        description: `Field "${field}" updated for ${selectedFileIds.size} file(s).`,
      });
    }, 0);
  }, [selectedFileIds, toast]);

  const handleExtractMetadata = useCallback((fieldsToExtract: (keyof Metadata)[], format: 'csv') => {
    if (files.length === 0) {
      setTimeout(() => {
        toast({ title: "No files to extract from.", variant: "default"});
      }, 0);
      return;
    }
    if (format === 'csv') {
      const header = ['fileName', ...fieldsToExtract].join(',');
      const rows = files.map(file => {
        const rowData = [file.name];
        fieldsToExtract.forEach(field => {
          rowData.push(`"${(file.metadata[field] || '').toString().replace(/"/g, '""')}"`);
        });
        return rowData.join(',');
      });

      const csvContent = [header, ...rows].join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement("a");
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", "metadata_extract.csv");
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        setTimeout(() => {
          toast({
            title: "Metadata Extracted",
            description: `Data for ${files.length} file(s) has been downloaded as CSV.`,
          });
        }, 0);
      } else {
        setTimeout(() => {
          toast({
            variant: "destructive",
            title: "Download Failed",
            description: "Your browser does not support direct file downloads.",
          });
        }, 0);
      }
    }
  }, [files, toast]);

  const handleApplyRename = useCallback((pattern: string) => {
    setFiles(prevFiles =>
      prevFiles.map(file => {
        if (selectedFileIds.has(file.id)) {
          let newNameBase = pattern;
          newNameBase = newNameBase.replace(/\[Title\]/g, sanitizeFilenamePart(file.metadata.title || ''));
          newNameBase = newNameBase.replace(/\[Author\]/g, sanitizeFilenamePart(file.metadata.author || ''));
          newNameBase = newNameBase.replace(/\[Subject\]/g, sanitizeFilenamePart(file.metadata.subject || ''));

          const originalNameWithoutExt = file.originalNameOnUpload.substring(0, file.originalNameOnUpload.lastIndexOf('.')) || file.originalNameOnUpload;
          newNameBase = newNameBase.replace(/\[OriginalFileName\]/g, sanitizeFilenamePart(originalNameWithoutExt));

          newNameBase = newNameBase.trim() || 'untitled';
          newNameBase = sanitizeFilenamePart(newNameBase);

          const extension = file.originalNameOnUpload.substring(file.originalNameOnUpload.lastIndexOf('.'));
          const finalNewName = `${newNameBase}${extension}`;

          return { ...file, name: finalNewName };
        }
        return file;
      })
    );
    setTimeout(() => {
      toast({
        title: "Files Renamed",
        description: `${selectedFileIds.size} file(s) have been renamed according to the pattern.`,
      });
    }, 0);
  }, [selectedFileIds, toast]);

  const firstSelectedFile = useMemo(() => {
    if (selectedFileIds.size === 0) return undefined;
    const firstId = Array.from(selectedFileIds)[0];
    return files.find(f => f.id === firstId);
  }, [selectedFileIds, files]);

  const availableMetadataFields = useMemo(() => {
    if (files.length > 0 && files[0].metadata) {
      return Object.keys(files[0].metadata) as (keyof Metadata)[];
    }
    return ['title', 'author', 'subject', 'keywords', 'producer', 'creatorTool'] as (keyof Metadata)[];
  }, [files]);

  const showAdPlaceholder = useMemo(() => {
    return user && subscription && subscription.planId === 'free' && !loadingSubscription;
  }, [user, subscription, loadingSubscription]);

  const allFilesExpanded = useMemo(() => {
    if (files.length === 0) return false;
    return openFileAccordionItems.length === files.length;
  }, [files, openFileAccordionItems]);


  return (
    <div className="min-h-screen flex flex-col items-center py-6 md:py-10 px-4 bg-background text-foreground font-body">
      <header className="w-full mb-8 text-center">
        {/* Wrap the image in a div for the circular shape, padding, shadow, and centering */}
 <div className="inline-flex items-center justify-center bg-secondary rounded-full p-8 shadow-lg mb-4 overflow-hidden">
           <Image
            src="/ms-icon-150x150.png"
            alt="MetaPDF Logo"
            width={120}
            height={120}
            className="relative h-full w-full object-cover rounded-full"
            priority
            data-ai-hint="app icon"
           />

        </div>
        <h1 className="text-4xl md:text-5xl font-headline font-bold text-primary sr-only">MetaPDF</h1> {/* Screen-reader only if logo contains name */}
        <p className="text-lg text-muted-foreground mt-2 max-w-2xl mx-auto">
          Streamline your PDF metadata management. Edit, extract, and optimize for SEO effortlessly.
        </p>
      </header>

      {showAdPlaceholder && (
        <div className="w-full max-w-2xl mb-6 p-3 bg-muted/50 border border-border rounded-lg text-center">
          <p className="text-sm text-muted-foreground">Advertisement Placeholder - <Link href="/pricing" className="underline text-primary hover:text-primary/80">Upgrade to Pro</Link> for an Ad-Free Experience!</p>
        </div>
      )}

      <div className="w-full flex flex-col items-center">
        <FileOperationErrorBoundary
          onRetry={() => window.location.reload()}
          onClearFiles={() => setFiles([])}
        >
          <FileUploadDropzone onFilesAdded={handleFilesAdded} />
        </FileOperationErrorBoundary>

        {files.length > 0 && (
          <div className="w-full max-w-6xl mt-10">
            <ErrorBoundary level="component">
              <MetadataControls
              onBulkEditClick={() => setIsBulkEditModalOpen(true)}
              onApplyTemplateClick={() => {
                if (!user) {
                  setTimeout(() => {
                    toast({title: "Login Required", description: "Please login to use templates.", variant: "destructive"});
                  }, 0);
                  return;
                }
                if (subscription?.planId === 'free') {
                   setTimeout(() => {
                     toast({title: "Pro Feature", description: "Applying templates is a Pro feature. Please upgrade your plan.", variant: "default", action: (<Button variant="outline" size="sm" asChild><Link href="/pricing">Upgrade</Link></Button>)});
                   }, 0);
                   return;
                }
                setIsApplyTemplateModalOpen(true)
              }}
              onRenameClick={() => setIsRenameModalOpen(true)}
              onExtractClick={() => {
                if (subscription?.planId === 'free') {
                   setTimeout(() => {
                     toast({title: "Pro Feature", description: "Extracting all metadata is a Pro feature. Please upgrade your plan.", variant: "default", action: (<Button variant="outline" size="sm" asChild><Link href="/pricing">Upgrade</Link></Button>)});
                   }, 0);
                   return;
                }
                setIsExtractModalOpen(true)
              }}
              onSaveAllClick={handleSaveAll}
              onClearAllClick={handleConfirmClearAll}
              isSeoView={isSeoView}
              onToggleSeoView={handleToggleSeoView}
              hasFiles={files.length > 0}
              hasSelection={selectedFileIds.size > 0}
              isLoadingTemplates={isLoadingTemplates}
              isLoadingSubscription={loadingSubscription}
              onExpandAllFiles={handleExpandAllFiles}
              onCollapseAllFiles={handleCollapseAllFiles}
              allFilesExpanded={allFilesExpanded}
              />
            </ErrorBoundary>

            <AIOperationErrorBoundary
              operationType="suggestion"
              onRetry={() => window.location.reload()}
              onSkipAI={() => {
                // Continue without AI functionality
                console.log('Continuing without AI features');
              }}
            >
              <MetadataDisplayTable
              files={files}
              onUpdateFile={updateFileMetadata}
              onSelectFile={handleSelectFile}
              selectedFileIds={selectedFileIds}
              isSeoView={isSeoView}
              openAccordionItems={openFileAccordionItems}
              onOpenAccordionItemsChange={setOpenFileAccordionItems}
              />
            </AIOperationErrorBoundary>
          </div>
        )}
         {files.length === 0 && (
           <Card className="mt-12 text-center p-8 border-dashed border-border rounded-lg bg-card shadow-sm max-w-md w-full">
              <FileIcon className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold text-card-foreground mb-2">Start Managing Your PDFs</h2>
              <p className="text-muted-foreground">
                Drag and drop your PDF files into the area above to begin editing their metadata.
                You can bulk edit, optimize for SEO, and extract information quickly.
              </p>
            </Card>
         )}
      </div>

      {isBulkEditModalOpen && <BulkEditDialog
        isOpen={isBulkEditModalOpen}
        onClose={() => setIsBulkEditModalOpen(false)}
        onApplyBulkEdit={handleApplyBulkEdit}
        numFilesSelected={selectedFileIds.size}
      />}
      {isExtractModalOpen && <ExtractMetadataDialog
        isOpen={isExtractModalOpen}
        onClose={() => setIsExtractModalOpen(false)}
        onExtract={handleExtractMetadata}
        metadataFields={availableMetadataFields}
        numFiles={files.length}
      />}
      {user && isApplyTemplateModalOpen && <ApplyTemplateDialog
        isOpen={isApplyTemplateModalOpen}
        onClose={() => setIsApplyTemplateModalOpen(false)}
        templates={templates}
        onCreateTemplate={handleCreateTemplate}
        onApplyTemplate={handleApplyTemplate}
        numFilesSelected={selectedFileIds.size}
      />}
      {isRenameModalOpen && <RenameFilesDialog
        isOpen={isRenameModalOpen}
        onClose={() => setIsRenameModalOpen(false)}
        onApplyRename={handleApplyRename}
        numFilesSelected={selectedFileIds.size}
        firstSelectedFile={firstSelectedFile}
      />}
      {isConfirmClearOpen && <ConfirmClearDialog
        isOpen={isConfirmClearOpen}
        onClose={() => setIsConfirmClearOpen(false)}
        onConfirm={executeClearAll}
      />}


      <footer className="w-full mt-16 py-8 border-t border-border text-center">
        <div className="mb-1">
            <Link href="/privacy-policy" className="text-xs text-muted-foreground hover:text-primary transition-colors px-1">
                Privacy Policy
            </Link>
            <span className="text-xs text-muted-foreground px-1">|</span>
            <Link href="/terms-of-service" className="text-xs text-muted-foreground hover:text-primary transition-colors px-1">
                Terms of Service
            </Link>
        </div>
        <p className="text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} MetaPDF. All rights reserved.
        </p>
         <p className="text-xs text-muted-foreground mt-1">
            Built with care.
          </p>
      </footer>
    </div>
  );
}
